"""
AIPPT相关API模型
请求和响应的数据模型定义
"""

from pydantic import BaseModel, Field
from typing import Optional, Dict, Any


# ==================== 获取认证code相关模型 ====================


class GetPPTAuthCodeResponse(BaseModel):
    """获取PPT认证code响应"""

    code: str = Field(..., description="认证code")
    time_expire: str = Field(..., description="认证code有效期")
    api_key: str = Field(..., description="AIPPT API Key")
    channel: str = Field(..., description="渠道标识")

class AIPPTAuthCodeResponse:
    """AIPPT认证码响应"""

    def __init__(self, code: str, time_expire: str, api_key: str, channel: str):
        self.code = code
        self.time_expire = time_expire
        self.api_key = api_key
        self.channel = channel


class AIPPTTokenResponse:
    """AIPPT token响应"""

    def __init__(self, token: str, time_expire: str):
        self.token = token
        self.time_expire = time_expire


# ==================== 保存PPT相关模型 ====================


class SavePPTRequest(BaseModel):
    """保存PPT请求"""

    file_id: str = Field(..., description="作品ID")
    thumbnail_url: Optional[str] = Field(None, description="封面图url")


# ==================== 下载PPT相关模型 ====================


class DownloadPPTRequest(BaseModel):
    """下载PPT请求"""

    file_id: str = Field(..., description="作品ID")
    thumbnail_url: Optional[str] = Field(None, description="封面图url")


class DownloadPPTResponse(BaseModel):
    """下载PPT响应"""

    download_url: str = Field(..., description="作品下载url")


# ==================== 获取PPT封面图相关模型 ====================


class GetPPTThumbnailRequest(BaseModel):
    """获取PPT封面图请求"""

    file_id: str = Field(..., description="作品ID")


class GetPPTThumbnailResponse(BaseModel):
    """获取PPT封面图响应"""

    thumbnail_url: str = Field(..., description="封面图url")


# ==================== 通用错误响应模型 ====================


class PPTErrorResponse(BaseModel):
    """PPT服务错误响应"""

    error_code: str = Field(..., description="错误码")
    error_message: str = Field(..., description="错误信息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
