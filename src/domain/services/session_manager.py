"""
Pythonic会话管理器
使用上下文管理器、简化的设计模式
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, Optional, Any, List, AsyncGenerator
from loguru import logger

from ..models import Session, Message, SessionStatus, Role
from ...infrastructure.memory.memory_sdk import MemorySD<PERSON>
from .state_machine import SessionStateMachine
from .message_processor import MessageProcessor
from .sse_manager import SSEManager


class SessionManager:
    """会话管理器 - 使用Pythonic设计"""
    
    def __init__(self):
        self.sessions: Dict[str, Session] = {}
        self.state_machine = SessionStateMachine()
        
        # 组件化模块
        self.message_processor = MessageProcessor()
        self.sse_manager = SSEManager()
        
        # 设置组件间的引用关系
        self.message_processor.set_sse_manager(self.sse_manager)
        self.message_processor.set_session_manager(self)
        # 为SSEManager创建适配器函数，因为它期望的是旧的参数格式
        self.sse_manager.set_session_loader(self)
        
        # 外部依赖
        #self.waiy_client: Optional[WaiyInfraClient] = None
        self.memory_sdk: Optional[MemorySDK] = None
        
        logger.info("[SessionManager] 初始化完成")
    
    async def initialize(self):
        """初始化管理器"""
        try:
            # 先初始化MemorySDK
            self.memory_sdk = MemorySDK()
            
            # 然后初始化WaiyInfraClient
            # self.waiy_client = WaiyInfraClient()
            
            # 注册Memory SDK回调
            # self.memory_sdk.register_callback("on_new_message", self.message_processor.handle_new_message)
            # self.memory_sdk.register_callback("on_round_failed", self.message_processor.handle_round_failed)
            
            # 启动清理任务
            asyncio.create_task(self._cleanup_task())
            
            logger.info("[SessionManager] 管理器初始化完成")
            
        except Exception as e:
            logger.error(f"[SessionManager] 初始化失败: {e}")
            raise
    
    # ================ Session管理 ================
    
    def get_or_create_session(self, session_id: Optional[str], ali_uid: str, agent_id: str, wy_id: str = "") -> Session:
        """获取或创建Session"""
        # 从数据库操作
        from ...infrastructure.database.repositories.session_repository import session_db_service
        
        if session_id:
            # 从数据库查询
            session_model = session_db_service.get_session_by_id(session_id)
            if session_model:
                # 从数据库模型重建Session对象
                session = Session(
                    session_id=session_model.session_id,
                    ali_uid=session_model.ali_uid,
                    agent_id=session_model.agent_id,
                    wy_id=session_model.wy_id,
                    title=session_model.title,
                    status=session_model.status,
                    gmt_create=session_model.gmt_create,
                    gmt_modified=session_model.gmt_modified,
                    metadata=session_model.meta_data or {}
                )
                
                # 设置事件监听
                self._setup_session_events(session)
                
                logger.info(f"[SessionManager] 从数据库加载会话: {session_id}")
                return session
            else:
                # Session不存在
                raise ValueError(f"Session不存在: {session_id}")
        
        # 创建新Session
        session = Session.create_new(ali_uid, agent_id, wy_id=wy_id, session_id=session_id)
        
        # 保存到数据库
        try:
            session_model = session_db_service.create_session(
                ali_uid=ali_uid,
                agent_id=agent_id,
                session_id=session.session_id,
                title=session.title,
                metadata=session.metadata
            )
            logger.info(f"[SessionManager] Session已保存到数据库: {session.session_id}")
        except Exception as e:
            logger.error(f"[SessionManager] 保存Session到数据库失败: {e}")
            # 继续执行，不影响内存操作
        
        # 设置事件监听
        self._setup_session_events(session)
        
        status_name = getattr(session.status, 'name', str(session.status))
        logger.info(f"[SessionManager] 创建新会话: {session.session_id} 状态: {status_name}")
        return session
    
    async def send_message(self, session_id: Optional[str], prompt: str, agent_id: str, ali_uid: str = "mock_user", wy_id: str = "") -> tuple[str, str]:
        """发送消息"""
        session = self.get_or_create_session(session_id, ali_uid, agent_id, wy_id)
        
        logger.info(f"[SessionManager] 发送消息到waiy-infra: session_id={session.session_id}, prompt={prompt[:50]}...")
        
        # 调用外部服务
        try:
            result = await self.waiy_client.send_message(
                session_id=session.session_id,
                user_prompt=prompt,
                agent_id=agent_id,
                ali_uid=session.ali_uid,
            )
            
            if result.get("code") != 200:
                raise ValueError(f"发送消息失败: {result.get('msg')}")
            
            data = result.get("data", {})
            round_id = data.get("round_id")
            trace_id = data.get("trace_id")
            
            if not round_id:
                raise ValueError("waiy-infra未返回有效的round_id")
            
            round_obj = session.create_round_with_id(prompt, round_id, trace_id)
            session.start_processing(round_obj)
            
            logger.info(f"[SessionManager] 消息发送成功: session_id={session.session_id}, round_id={round_id}, trace_id={trace_id}")
            return session.session_id, round_id
            
        except Exception as e:
            logger.error(f"[SessionManager] 发送消息失败: {e}")
            # 确保Session状态恢复
            if session.is_active:
                session.finish_processing()
            raise
    
    # ================ SSE流管理 ================
    
    async def create_sse_stream(self, session_id: str, last_message_id: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """创建SSE流生成器"""
        async for event in self.sse_manager.create_sse_stream(session_id, last_message_id):
            yield event
    
    # ================ Session加载工具方法 ================
    
    def load_session_from_db(self, session_id: str) -> Optional[Session]:
        """从数据库加载Session"""
        try:
            from ...infrastructure.database.repositories.session_repository import session_db_service
            session_model = session_db_service.get_session_by_id(session_id)
            if not session_model:
                return None
            
            # 从数据库模型重建Session对象
            session = Session(
                session_id=session_model.session_id,
                ali_uid=session_model.ali_uid,
                agent_id=session_model.agent_id,
                wy_id=session_model.wy_id,
                title=session_model.title,
                status=session_model.status,
                gmt_create=session_model.gmt_create,
                gmt_modified=session_model.gmt_modified,
                metadata=session_model.meta_data or {}
            )
            
            return session
            
        except Exception as e:
            logger.error(f"[SessionManager] 从数据库加载Session失败: {e}")
            return None
    
    def setup_session_events(self, session: Session):
        """设置Session事件监听"""
        self._setup_session_events(session)
    
    # ================ 事件回调 ================
    
    def _setup_session_events(self, session: Session):
        """设置Session事件监听"""
        session.on('status_changed', self._on_session_status_changed)

    def _on_session_status_changed(self, session: Session, old_status: SessionStatus, new_status: SessionStatus, reason: str):
        """Session状态变更回调"""
        old_status_name = getattr(old_status, 'name', str(old_status))
        new_status_name = getattr(new_status, 'name', str(new_status))
        logger.info(f"[SessionManager] Session状态变更: {session.session_id} {old_status_name} -> {new_status_name} ({reason})")

        # 同步状态到数据库
        try:
            from ...infrastructure.database.repositories.session_repository import session_db_service
            session_db_service.update_session_status(
                session_id=session.session_id,
                status=new_status,
                title=session.title
            )
            logger.debug(f"[SessionManager] Session状态已同步到数据库: {session.session_id}")
        except Exception as e:
            logger.error(f"[SessionManager] 同步Session状态到数据库失败: {e}")
    

    # ================ 清理方法 ================
    
    async def _cleanup_task(self):
        """清理过期会话的后台任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 每5分钟清理一次
                self.cleanup_expired_sessions()
            except Exception as e:
                logger.error(f"[SessionManager] 清理任务异常: {e}")
    
    def cleanup_expired_sessions(self):
        """清理过期Session缓存"""
        try:
            # 清理过期的SSE连接
            self.sse_manager.cleanup_expired_connections()
        
        except Exception as e:
            logger.error(f"[SessionManager] 清理缓存失败: {e}")
    
    async def close(self):
        """关闭管理器"""
        try:
            # 关闭外部客户端
            if self.waiy_client:
                await self.waiy_client.close()
            
            # 关闭Memory客户端
            if hasattr(self, 'memory_sdk') and self.memory_sdk:
                self.memory_sdk.close()
            
            # 清理资源
            self.sse_manager.sse_connections.clear()
            
            logger.info("[SessionManager] 管理器已关闭")
        
        except Exception as e:
            logger.error(f"[SessionManager] 关闭管理器失败: {e}")


# 全局管理器实例
session_manager = SessionManager() 