#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT业务服务
处理PPT相关的业务逻辑，包括认证、生成、保存和下载等功能
"""
from datetime import datetime
from typing import Optional, Dict, Any
from src.application.ppt_api_models import GetPPTAuthCodeResponse
from loguru import logger

from ...popclients.aippt_client import (
    get_aippt_client,
    AIPPTClientError,
    AIPPTAuthCodeResponse,
)
from src.infrastructure.memory.memory_sdk import MemorySDK
from src.domain.services.auth_service import AuthContext
from src.infrastructure.redis.client import RedisClient


class PPTService:
    """PPT业务服务"""

    def __init__(self):
        self.aippt_client = None  # 延迟初始化
        self.memory_sdk = None  # 延迟初始化
        logger.info("[PPTService] PPT服务初始化完成")

    def _get_aippt_client(self):
        """获取AIPPT客户端实例"""
        if self.aippt_client is None:
            self.aippt_client = get_aippt_client()
        return self.aippt_client

    def _get_memory_sdk(self):
        """获取MemorySDK实例"""
        if self.memory_sdk is None:
            self.memory_sdk = MemorySDK()
        return self.memory_sdk

    def _get_aippt_token(self):
        """
        获取AIPPT token，优先从缓存获取，如果不存在则重新获取
        """
        logger.info(f"[PPTService] 获取AIPPT token")

        # 优先从缓存中获取token
        redis_client = RedisClient()
        token = redis_client.get(f"aippt_token")
        if token:
            logger.info(f"[PPTService] 从缓存中获取AIPPT token: {token}")
            return token

        # 如果缓存中没有token，则重新获取，并缓存
        logger.info(f"[PPTService] 从缓存中没有获取到AIPPT token，重新获取")
        client = self._get_aippt_client()
        response = client.get_aippt_token()
        redis_client.set(f"aippt_token", response.token, int(response.time_expire))
        
        return response.token

    def get_ppt_auth_code(
        self,
        ali_uid: int,
    ) -> GetPPTAuthCodeResponse:
        """
        获取PPT认证code

        Args:
            ali_uid: 用户ID

        Returns:
            GetPPTAuthCodeResponse: 认证码响应
        """
        logger.info(f"[PPTService] 获取PPT认证code: ali_uid={ali_uid}")

        client = self._get_aippt_client()
        response = client.get_aippt_auth_code(ali_uid=ali_uid)

        return GetPPTAuthCodeResponse(
            code=response.code,
            time_expire=str(response.time_expire),
            api_key=response.api_key,
            channel=response.channel,
        )

    def save_ppt(
        self,
        file_id: str,
        thumbnail_url: Optional[str] = None,
    ):
        """
        保存PPT到制品管理

        Args:
            file_id: 作品ID
            thumbnail_url: 封面图URL
        """
        try:
            logger.info(
                f"[PPTService] 保存PPT: file_id={file_id}, thumbnail_url={thumbnail_url}"
            )

            # TODO: 调用AIPPT客户端保存PPT
            # client = self._get_aippt_client()
            # save_response = client.save_ppt(
            #     login_token=login_token,
            #     login_session_id=login_session_id,
            #     file_id=file_id,
            #     thumbnail_url=thumbnail_url,
            #     region_id=region_id
            # )

            logger.warning(f"[PPTService] 保存PPT功能待实现")
            raise NotImplementedError("保存PPT功能尚未实现")

        except Exception as e:
            logger.error(f"[PPTService] 保存PPT失败: {e}")
            raise PPTServiceError(f"保存PPT失败: {e}") from e

    def download_ppt(
        self,
        file_id: str,
        region_id: Optional[str] = None,
    ) -> str:
        """
        下载PPT

        Args:
            file_id: 作品ID
            region_id: 区域ID

        Returns:
            str: 下载链接
        """
        try:
            logger.info(
                f"[PPTService] 下载PPT: file_id={file_id}, region_id={region_id}"
            )

            # TODO: 调用AIPPT客户端下载PPT
            # client = self._get_aippt_client()
            # download_response = client.download_ppt(
            #     login_token=login_token,
            #     login_session_id=login_session_id,
            #     file_id=file_id,
            #     thumbnail_url=None,
            #     region_id=region_id
            # )

            logger.warning(f"[PPTService] 下载PPT功能待实现")
            raise NotImplementedError("下载PPT功能尚未实现")

        except Exception as e:
            logger.error(f"[PPTService] 下载PPT失败: {e}")
            raise PPTServiceError(f"下载PPT失败: {e}") from e

    def get_ppt_thumbnail(
        self,
        file_id: str,
        region_id: Optional[str] = None,
    ) -> str:
        """
        获取PPT封面图

        Args:
            file_id: 作品ID
            region_id: 区域ID

        Returns:
            str: 封面图URL
        """
        try:
            logger.info(
                f"[PPTService] 获取PPT封面图: file_id={file_id}, region_id={region_id}"
            )

            # TODO: 调用AIPPT客户端获取封面图
            # client = self._get_aippt_client()
            # thumbnail_response = client.get_ppt_thumbnail(
            #     login_token=login_token,
            #     login_session_id=login_session_id,
            #     file_id=file_id,
            #     region_id=region_id
            # )

            logger.warning(f"[PPTService] 获取PPT封面图功能待实现")
            raise NotImplementedError("获取PPT封面图功能尚未实现")

        except Exception as e:
            logger.error(f"[PPTService] 获取PPT封面图失败: {e}")
            raise PPTServiceError(f"获取PPT封面图失败: {e}") from e

    async def create_ppt_from_session_history(
        self,
        auth_context: AuthContext,
        session_id: str,
        title: Optional[str] = None,
        round_ids: Optional[list] = None,
    ) -> Dict[str, Any]:
        """
        从会话历史创建PPT

        Args:
            auth_context: 认证上下文
            session_id: 会话ID
            title: PPT标题
            round_ids: 指定的轮次ID列表，如果为None则使用所有轮次

        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            logger.info(
                f"[PPTService] 从会话历史创建PPT: session_id={session_id}, "
                f"title={title}, round_ids={round_ids}, ali_uid={auth_context.ali_uid}"
            )

            # TODO: 实现从会话历史创建PPT的功能
            # 由于memory_sdk的方法是异步的，这里暂时简化实现
            logger.warning("[PPTService] 从会话历史创建PPT功能待实现")

            # 构造响应数据
            result_title = title or f"会话PPT-{session_id[:8]}"

            return {
                "success": True,
                "message": "PPT创建请求已提交",
                "title": result_title,
                "session_id": session_id,
                "created_by": auth_context.ali_uid,
                "created_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"[PPTService] 从会话历史创建PPT失败: {e}")
            raise PPTServiceError(f"创建PPT失败: {e}") from e


class PPTServiceError(Exception):
    """PPT服务异常"""

    pass


# 全局单例实例
ppt_service = PPTService()
