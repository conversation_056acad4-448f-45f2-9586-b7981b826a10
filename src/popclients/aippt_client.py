# -*- coding: utf-8 -*-
"""
AIPPT服务客户端封装
提供简化的接口来访问AIPPT服务
"""
import json
import time
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from urllib.parse import urlencode, quote
import httpx
from loguru import logger
from src.shared.config.environments import env_manager
from src.infrastructure.redis import RedisClient
from src.application.ppt_api_models import AIPPTAuthCodeResponse, AIPPTTokenResponse


class AIPPTClient:
    """
    AIPPT服务客户端封装类
    """

    def __init__(
        self,
        access_key_id: Optional[str] = None,
        access_key_secret: Optional[str] = None,
        endpoint: Optional[str] = None,
        connect_timeout: int = 10000,
        read_timeout: int = 10000,
        **kwargs,
    ):
        """
        初始化AIPPT服务客户端

        Args:
            access_key_id: aippt访问密钥ID
            access_key_secret: aippt访问密钥Secret
            endpoint: 服务端点，默认为AIPPT服务端点
            connect_timeout: 连接超时时间（毫秒）
            read_timeout: 读取超时时间（毫秒）
            **kwargs: 其他配置参数
        """

        # 从配置文件中获取endpoint
        if endpoint is None:
            endpoint = env_manager.get_config_value("aippt_endpoint")

        # 从配置文件获取密钥
        if not access_key_id:
            access_key_id = env_manager.get_config_value("aippt_access_key")
        if not access_key_secret:
            access_key_secret = env_manager.get_config_value("aippt_secret_key")

        if not access_key_id:
            raise AIPPTClientError(
                "access_key_id不能为空，请在配置文件中设置aippt_access_key"
            )
        if not access_key_secret:
            raise AIPPTClientError(
                "access_key_secret不能为空，请在配置文件中设置aippt_secret_key"
            )

        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.endpoint = endpoint
        self.connect_timeout = connect_timeout / 1000  # 转换为秒
        self.read_timeout = read_timeout / 1000  # 转换为秒

        # 初始化HTTP客户端
        self.http_client = httpx.Client(
            timeout=httpx.Timeout(
                connect=self.connect_timeout,
                read=self.read_timeout,
                write=self.connect_timeout,
                pool=self.connect_timeout,
            )
        )

        logger.info(
            f"AIPPT客户端初始化完成: endpoint={endpoint}, access_key_id={access_key_id[:8]}..."
        )

    def _generate_signature(self, method: str, uri: str, timestamp: int) -> str:
        """
        根据AIPPT官方文档生成签名

        签名算法：
        1. 构造规范请求字符串：HttpRequestMethod + "@" + ApiUri + "@" + Timestamp
        2. URI路径必须以"/"结尾，如 /api/grant/code/
        3. 时间戳单位为秒
        4. 使用HMAC-SHA1算法和API密钥对字符串进行签名
        5. 对签名结果进行Base64编码

        Args:
            method: HTTP请求方法，如 GET、POST
            uri: 请求资源路径，如 /api/grant/code
            timestamp: 时间戳（秒）

        Returns:
            签名字符串
        """
        # 1. 确保URI以"/"结尾
        if not uri.endswith('/'):
            uri = uri + '/'

        # 2. 构建待签名字符串：HttpRequestMethod + "@" + ApiUri + "@" + Timestamp
        string_to_sign = f"{method}@{uri}@{timestamp}"

        logger.debug(f"[AIPPT签名] 待签名字符串: {string_to_sign}")

        # 3. 使用HMAC-SHA1生成签名
        signature = hmac.new(
            self.access_key_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha1,  # 使用SHA1算法
        ).digest()

        # 4. Base64编码
        signature_b64 = base64.b64encode(signature).decode('utf-8')

        logger.debug(f"[AIPPT签名] 生成签名: {signature_b64}")

        return signature_b64

    def get_aippt_auth_code(
        self,
        ali_uid: int,
    ) -> AIPPTAuthCodeResponse:
        """
        获取PPT认证code

        根据AIPPT官方文档：
        - 接口地址: /api/grant/code
        - 请求方式: GET
        - 参数: uid（用户ID，对应ali_uid）、channel（渠道，传空）、type（类型，不传）
        - 认证: HTTP Header中的 x-api-key、x-timestamp、x-signature

        Args:
            ali_uid: 用户ID（对应AIPPT的uid参数）

        Returns:
            AIPPTAuthCodeResponse: 认证码响应
        """

        try:
            logger.info(f"[AIPPT API] 开始获取PPT认证code: uid={ali_uid}")

            params = {'uid': ali_uid, 'channel': ''}
            timestamp = int(time.time())
            signature = self._generate_signature('GET', '/api/grant/code', timestamp)
            url = f"https://{self.endpoint}/api/grant/code"
            headers = {
                'x-api-key': self.access_key_id,
                'x-timestamp': str(timestamp),
                'x-signature': signature,
                'Content-Type': 'application/json',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self.http_client.get(url, params=params, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            data = response_data.get('data', {})
            auth_code = data.get('code')
            time_expire = data.get('time_expire')
            api_key = data.get('api_key')
            channel = env_manager.get_config_value("aippt_channel")

            if not auth_code:
                raise AIPPTClientError(f"响应中缺少认证码: {response_data}")

            return AIPPTAuthCodeResponse(
                code=auth_code,
                time_expire=time_expire,
                api_key=api_key,
                channel=channel,
            )
        except Exception as e:
            raise AIPPTClientError(f"获取PPT认证code失败: {str(e)}") from e

    def get_aippt_token(self) -> AIPPTTokenResponse:
        """
        获取AIPPT token

        根据AIPPT官方文档：
        - 接口地址: /api/grant/token
        - 请求方式: GET
        - 参数: uid（用户ID，从配置中获取aippt_token_uid）
        - 认证: HTTP Header中的 x-api-key、x-timestamp、x-signature

        Returns:
            AIPPTTokenResponse: token响应对象
        """
        try:
            # 从配置获取token请求的uid
            token_uid = env_manager.get_config_value("aippt_token_uid")
            if not token_uid:
                raise AIPPTClientError("aippt_token_uid未在配置文件中设置")

            logger.info(f"[AIPPT API] 开始获取AIPPT token: uid={token_uid}")

            params = {'uid': token_uid}
            timestamp = int(time.time())
            signature = self._generate_signature('GET', '/api/grant/token', timestamp)
            url = f"https://{self.endpoint}/api/grant/token"
            headers = {
                'x-api-key': self.access_key_id,
                'x-timestamp': str(timestamp),
                'x-signature': signature,
                'Content-Type': 'application/json',
                'User-Agent': 'Alpha-Service-AIPPT-Client/1.0',
            }

            response = self.http_client.get(url, params=params, headers=headers)

            if response.status_code != 200:
                raise AIPPTClientError(
                    f"HTTP请求失败，状态码: {response.status_code}, 响应: {response.text}"
                )

            try:
                response_data = response.json()
            except json.JSONDecodeError as e:
                raise AIPPTClientError(
                    f"解析JSON响应失败: {str(e)}, 响应内容: {response.text}"
                )

            # AIPPT API中 code: 0 表示成功，非0表示失败
            if 'code' in response_data and response_data.get('code') != 0:
                error_message = response_data.get('msg', '未知错误')
                raise AIPPTClientError(
                    f"AIPPT API调用失败: {error_message} (Code: {response_data.get('code')})"
                )

            data = response_data.get('data', {})
            if not data:
                raise AIPPTClientError(f"响应中缺少数据: {response_data}")

            token = data.get('token')
            time_expire = data.get('time_expire')

            logger.info(f"[AIPPT API] 成功获取AIPPT token: token={token}, time_expire={time_expire}")
            return AIPPTTokenResponse(
                token=token,
                time_expire=time_expire,
            )

        except Exception as e:
            raise AIPPTClientError(f"获取AIPPT token失败: {str(e)}") from e

    def save_ppt(
        self,
        login_token: str,
        login_session_id: str,
        file_id: str,
        thumbnail_url: Optional[str] = None,
        region_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        保存PPT

        Args:
            login_token: 登录令牌
            login_session_id: 登录会话ID
            file_id: 作品ID
            thumbnail_url: 封面图URL
            region_id: 区域ID

        Returns:
            Dict[str, Any]: 保存响应
        """
        logger.warning(f"保存PPT功能待实现: file_id={file_id}")
        raise NotImplementedError("保存PPT功能尚未实现")

    def download_ppt(
        self,
        login_token: str,
        login_session_id: str,
        file_id: str,
        thumbnail_url: Optional[str] = None,
        region_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        下载PPT

        Args:
            login_token: 登录令牌
            login_session_id: 登录会话ID
            file_id: 作品ID
            thumbnail_url: 封面图URL
            region_id: 区域ID

        Returns:
            Dict[str, Any]: 下载响应，包含下载链接
        """
        logger.warning(f"下载PPT功能待实现: file_id={file_id}")
        raise NotImplementedError("下载PPT功能尚未实现")

    def get_ppt_thumbnail(
        self,
        login_token: str,
        login_session_id: str,
        file_id: str,
        region_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取PPT封面图

        Args:
            login_token: 登录令牌
            login_session_id: 登录会话ID
            file_id: 作品ID
            region_id: 区域ID

        Returns:
            Dict[str, Any]: 封面图响应，包含封面图URL
        """
        logger.warning(f"获取PPT封面图功能待实现: file_id={file_id}")
        raise NotImplementedError("获取PPT封面图功能尚未实现")

    def __del__(self):
        """析构函数，清理HTTP客户端"""
        if hasattr(self, 'http_client'):
            try:
                self.http_client.close()
            except:
                pass


class AIPPTClientError(Exception):
    """AIPPT客户端异常类"""

    pass


# 全局单例实例
_aippt_client_instance = None


def get_aippt_client(
    access_key_id: Optional[str] = None,
    access_key_secret: Optional[str] = None,
    endpoint: Optional[str] = None,
    **kwargs,
) -> AIPPTClient:
    """
    获取AIPPT客户端单例实例

    Args:
        access_key_id: aippt访问密钥ID
        access_key_secret: aippt访问密钥Secret
        endpoint: 服务端点
        **kwargs: 其他配置参数

    Returns:
        AIPPTClient: AIPPT客户端实例
    """
    global _aippt_client_instance

    if _aippt_client_instance is None:
        _aippt_client_instance = AIPPTClient(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret,
            endpoint=endpoint or "co.aippt.cn",
            **kwargs,
        )

    return _aippt_client_instance


def reset_aippt_client():
    """重置AIPPT客户端单例实例"""
    global _aippt_client_instance
    _aippt_client_instance = None
