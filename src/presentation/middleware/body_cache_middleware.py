#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
请求体缓存中间件
用于支持多次读取请求体，特别是为了支持从JSON body中提取认证参数
"""
import json
from typing import Callable, Optional, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger


class BodyCacheMiddleware(BaseHTTPMiddleware):
    """
    请求体缓存中间件
    
    功能：
    1. 缓存POST请求的JSON body
    2. 支持多次读取请求体
    3. 将解析后的JSON数据存储在request.state中
    """
    
    def __init__(self, app):
        super().__init__(app)

    async def _log_request_details(
            self,
            request: Request
    ):
        """
        打印详细的 request 信息，用于调试

        Args:
            request: FastAPI Request 对象
        """
        try:
            logger.info("=" * 80)
            logger.info("[AuthService] 详细 Request 信息:")

            # 基本信息
            logger.info(f"  方法: {request.method}")
            logger.info(f"  URL: {request.url}")
            logger.info(f"  路径: {request.url.path}")
            logger.info(f"  客户端IP: {request.client.host if request.client else 'Unknown'}")

            # Query Parameters
            logger.info(f"  Query Parameters:")
            if request.query_params:
                for key, value in request.query_params.items():
                    # 对敏感信息进行脱敏
                    if key.lower() in ['password', 'token', 'secret', 'key']:
                        value = '***'
                    logger.info(f"    {key}: {value}")
            else:
                logger.info(f"    (无 query parameters)")

            # Headers
            logger.info(f"  Headers:")
            if request.headers:
                for key, value in request.headers.items():
                    # 对敏感信息进行脱敏
                    if key.lower() in ['authorization', 'cookie', 'x-api-key', 'x-auth-token']:
                        value = '***'
                    logger.info(f"    {key}: {value}")
            else:
                logger.info(f"    (无 headers)")

            # Body (如果是 POST/PUT 等方法)
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    # 尝试读取 body，但要小心不要消耗掉 stream
                    body = await request.body()
                    if body:
                        # 尝试解析为 JSON
                        try:
                            import json
                            body_json = json.loads(body.decode('utf-8'))
                            # 对敏感字段进行脱敏
                            if isinstance(body_json, dict):
                                sanitized_body = {}
                                for key, value in body_json.items():
                                    if key.lower() in ['password', 'token', 'secret', 'key', 'auth_code']:
                                        sanitized_body[key] = '***'
                                    else:
                                        sanitized_body[key] = value
                                logger.info(
                                    f"  Body (JSON): {json.dumps(sanitized_body, ensure_ascii=False, indent=2)}")
                            else:
                                logger.info(f"  Body (JSON): {body_json}")
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            # 如果不是 JSON，显示前100个字符
                            body_str = body.decode('utf-8', errors='ignore')[:100]
                            logger.info(f"  Body (Text): {body_str}{'...' if len(body) > 100 else ''}")
                    else:
                        logger.info(f"  Body: (空)")
                except Exception as body_error:
                    logger.warning(f"  Body: 无法读取 ({body_error})")

            # Path Parameters
            if hasattr(request, 'path_params') and request.path_params:
                logger.info(f"  Path Parameters:")
                for key, value in request.path_params.items():
                    logger.info(f"    {key}: {value}")
            else:
                logger.info(f"  Path Parameters: (无)")

            logger.info("=" * 80)

        except Exception as e:
            logger.error(f"[AuthService] 打印 request 详情时出错: {e}")

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求"""
        # 只处理POST请求且Content-Type为application/json的请求
        if request.method == "POST":
            await self._log_request_details(request)
        if (request.method == "POST" and 
            request.headers.get("content-type", "").startswith("application/json")):
            
            try:
                # 读取并缓存请求体
                body = await request.body()
                
                if body:
                    try:
                        # 解析JSON
                        json_data = json.loads(body.decode('utf-8'))
                        
                        # 将解析后的数据存储在request.state中
                        request.state.json_body = json_data
                        request.state.raw_body = body
                        
                        logger.debug(f"[BodyCacheMiddleware] 缓存请求体: path={request.url.path}, size={len(body)} bytes")
                        
                    except json.JSONDecodeError as e:
                        logger.warning(f"[BodyCacheMiddleware] JSON解析失败: {e}")
                        request.state.json_body = None
                        request.state.raw_body = body
                    except Exception as e:
                        logger.warning(f"[BodyCacheMiddleware] 处理请求体失败: {e}")
                        request.state.json_body = None
                        request.state.raw_body = body
                else:
                    request.state.json_body = None
                    request.state.raw_body = b""
                    
            except Exception as e:
                logger.error(f"[BodyCacheMiddleware] 读取请求体失败: {e}")
                request.state.json_body = None
                request.state.raw_body = b""
        else:
            # 非JSON POST请求，不缓存
            request.state.json_body = None
            request.state.raw_body = None
        
        # 继续处理请求
        response = await call_next(request)
        return response


def get_cached_json_body(request: Request) -> Optional[Dict[str, Any]]:
    """
    从request.state中获取缓存的JSON body
    
    Args:
        request: FastAPI Request对象
        
    Returns:
        Dict[str, Any]: 解析后的JSON数据，如果没有则返回None
    """
    return getattr(request.state, 'json_body', None)


def get_cached_raw_body(request: Request) -> Optional[bytes]:
    """
    从request.state中获取缓存的原始请求体
    
    Args:
        request: FastAPI Request对象
        
    Returns:
        bytes: 原始请求体数据，如果没有则返回None
    """
    return getattr(request.state, 'raw_body', None)


def extract_auth_params_from_json(json_data: Optional[Dict[str, Any]]) -> tuple[Optional[str], Optional[str], Optional[str]]:
    """
    从JSON数据中提取认证参数
    
    Args:
        json_data: 解析后的JSON数据
        
    Returns:
        tuple: (login_token, login_session_id, region_id)
    """
    if not json_data:
        return None, None, None
    
    login_token = json_data.get("loginToken")
    login_session_id = json_data.get("loginSessionId")
    region_id = json_data.get("regionId")
    
    return login_token, login_session_id, region_id

