import json
import traceback

from src.domain.utils.check_utils import ClientException
from loguru import logger
import uuid
from fastapi import Request

OK_STATUS = 200
FAIL_STATUS = 500
CLIENT_ERROR_STATUS = 400

#获取request id（外部有传入从外部优先获取，否则自己生成）
def get_request_id(request: Request):
    return request.query_params.get('request_id') or str(uuid.uuid4())

#尝试将结果打包为统一的格式
"""系统错误码"""
SYSTEM_ERROR_CODE = 500

def handle_exception(e: Exception, request_id: str) -> dict:
    """处理异常"""
    if isinstance(e, ClientException):
        return package_api_result(
            code=e.code,
            data=None,
            message=e.message,
            request_id=request_id,
            status=CLIENT_ERROR_STATUS,
            success=False,
        )
    else:
        logger.error(f"[API] call error: {e}\n{traceback.format_exc()}")
        return package_api_result(
            code=SYSTEM_ERROR_CODE,
            message="Sorry, something went wrong, please contact the administrator.",
            data=None,
            request_id=request_id,
            status=FAIL_STATUS,
            success=False,
        )


def package_api_result(
    code=None, 
    data=None, 
    message=None, 
    request_id=None, 
    status=None, 
    success=None, 
    dumps=False,
    total_count=0,
    next_token=None
):
    """包装API返回结果
    
    Args:
        code: 错误码，默认为200
        data: 返回数据，默认为空字典
        message: 消息，默认为空字符串
        request_id: 请求ID，默认为空字符串
        status: HTTP状态码，默认为200
        success: 是否成功，默认为False
        dumps: 是否序列化为JSON字符串，默认为False
    
    Returns:
        dict 或 str: 包装后的API结果
    """
    if code is None:
        code = 200
    if data is None:
        data = {}
    if message is None:
        message = ""
    if request_id is None:
        request_id = ""
    if status is None:
        status = OK_STATUS
    if success is None:
        success = True
    if total_count is None:
        total_count = None
    if next_token is None:
        next_token = None
    rlt = {
        "code": code,
        "data": data,
        "message": message,
        "request_id": request_id,
        "status": status,
        "success": success,
        "total_count": total_count,
        "next_token": next_token
    }
    # 利用fastapi内部的json序列化能力，正常情况应该不显示的dumps成json字符串了
    if dumps:
        rlt = json.dumps(rlt, ensure_ascii=False)
    return rlt
