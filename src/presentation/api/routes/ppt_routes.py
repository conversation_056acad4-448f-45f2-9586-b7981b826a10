"""
PPT相关API路由
处理PPT相关的HTTP接口，包括认证、生成、保存和下载等功能
"""

from typing import Optional, Dict, Any
from datetime import datetime
from src.domain.utils.check_utils import CheckUtils
from fastapi import APIRouter, HTTPException, Request, Query, Depends
from loguru import logger

from src.application.ppt_api_models import (
    GetPPTAuthCodeResponse,
    SavePPTRequest,
    DownloadPPTRequest,
    DownloadPPTResponse,
    GetPPTThumbnailRequest,
    GetPPTThumbnailResponse,
    PPTErrorResponse,
)
from src.domain.services.ppt_service import ppt_service, PPTServiceError
from src.domain.services.auth_service import AuthContext, require_auth
from src.presentation.api.dependencies.api_common_utils import (
    package_api_result,
    handle_exception,
    get_request_id,
)
from src.presentation.api.dependencies.common_params import CommonParams


router = APIRouter(prefix="/api/aippt", tags=["ppt"])


@router.get("/code/grant")
async def get_ppt_auth_code(
    current_user: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id),
):
    """
    获取PPT认证code
    """
    try:
        # 检查参数
        CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
        CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")

        logger.info(f"[PPT API] 获取认证code: ali_uid={current_user.ali_uid}")
        response = ppt_service.get_ppt_auth_code(
            ali_uid=current_user.ali_uid,
        )

        return package_api_result(
            data=response,
            request_id=request_id,
        )
    except Exception as e:
        return handle_exception(e, request_id)


# @router.post("/save")
# async def save_ppt(
#     request: SavePPTRequest,
#     current_user: AuthContext = Depends(require_auth),
#     request_id: str = Depends(get_request_id),
# ):
#     """
#     保存PPT
#     """
#     try:
#         CheckUtils.parameter_not_null(current_user.ali_uid, "ali_uid")
#         CheckUtils.parameter_not_null(current_user.wy_id, "wy_id")
#         CheckUtils.parameter_not_null(request.file_id, "file_id")

#         # 调用业务服务
#         ppt_service.save_ppt(
#             file_id=request.file_id, thumbnail_url=request.thumbnail_url
#         )

#         return package_api_result(
#             request_id=request_id,
#         )
#     except Exception as e:
#         return handle_exception(e, request_id)


# @router.post("/download")
# async def download_ppt(
#     request: DownloadPPTRequest,
#     context: AuthContext = Depends(require_auth),
#     request_id: str = Depends(get_request_id)
# ):
#     """
#     下载PPT

#     Args:
#         request: 下载PPT请求
#         context: 鉴权上下文
#         request_id: 请求ID

#     Returns:
#         包含下载URL的响应
#     """
#     try:
#         logger.info(f"[PPT API] 下载PPT: user={context.user_key}, file_id={request.file_id}")

#         # 调用业务服务
#         result = ppt_service.download_ppt(
#             context=context,
#             login_token=request.login_token,
#             login_session_id=request.login_session_id,
#             file_id=request.file_id,
#             thumbnail_url=request.thumbnail_url,
#             region_id=request.region_id
#         )

#         # 构造响应
#         response = DownloadPPTResponse(
#             download_url=result["download_url"]
#         )

#         logger.info(f"[PPT API] 下载PPT成功: user={context.user_key}, file_id={request.file_id}")

#         return package_api_result(
#             code=200,
#             message="获取下载链接成功",
#             data=response.model_dump(),
#             request_id=request_id,
#             success=True
#         )

#     except NotImplementedError as e:
#         logger.warning(f"[PPT API] 功能待实现: {str(e)}")
#         return package_api_result(
#             code=501,
#             message=f"功能待实现: {str(e)}",
#             data=None,
#             request_id=request_id,
#             success=False
#         )
#     except PPTServiceError as e:
#         logger.error(f"[PPT API] PPT服务错误: {str(e)}")
#         return package_api_result(
#             code=500,
#             message=f"PPT服务错误: {str(e)}",
#             data=None,
#             request_id=request_id,
#             success=False
#         )
#     except Exception as e:
#         logger.error(f"[PPT API] 下载PPT异常: {str(e)}")
#         return handle_exception(e, request_id)


# @router.get("/thumbnail")
# async def get_ppt_thumbnail(
#     file_id: str = Query(..., description="作品ID"),
#     login_token: str = Query(..., description="登录令牌"),
#     login_session_id: str = Query(..., description="登录会话ID"),
#     region_id: Optional[str] = Query(None, description="区域ID"),
#     context: AuthContext = Depends(require_auth),
#     request_id: str = Depends(get_request_id)
# ):
#     """
#     获取PPT封面图

#     Args:
#         file_id: 作品ID
#         login_token: 登录令牌
#         login_session_id: 登录会话ID
#         region_id: 区域ID
#         context: 鉴权上下文
#         request_id: 请求ID

#     Returns:
#         包含封面图URL的响应
#     """
#     try:
#         logger.info(f"[PPT API] 获取PPT封面图: user={context.user_key}, file_id={file_id}")

#         # 调用业务服务
#         result = ppt_service.get_ppt_thumbnail(
#             context=context,
#             login_token=login_token,
#             login_session_id=login_session_id,
#             file_id=file_id,
#             region_id=region_id
#         )

#         # 构造响应
#         response = GetPPTThumbnailResponse(
#             thumbnail_url=result["thumbnail_url"]
#         )

#         logger.info(f"[PPT API] 获取PPT封面图成功: user={context.user_key}, file_id={file_id}")

#         return package_api_result(
#             code=200,
#             message="获取封面图成功",
#             data=response.model_dump(),
#             request_id=request_id,
#             success=True
#         )

#     except NotImplementedError as e:
#         logger.warning(f"[PPT API] 功能待实现: {str(e)}")
#         return package_api_result(
#             code=501,
#             message=f"功能待实现: {str(e)}",
#             data=None,
#             request_id=request_id,
#             success=False
#         )
#     except PPTServiceError as e:
#         logger.error(f"[PPT API] PPT服务错误: {str(e)}")
#         return package_api_result(
#             code=500,
#             message=f"PPT服务错误: {str(e)}",
#             data=None,
#             request_id=request_id,
#             success=False
#         )
#     except Exception as e:
#         logger.error(f"[PPT API] 获取PPT封面图异常: {str(e)}")
#         return handle_exception(e, request_id)


# # ==================== 健康检查接口 ====================

# @router.get("/health")
# async def ppt_service_health():
#     """
#     PPT服务健康检查

#     Returns:
#         服务健康状态
#     """
#     try:
#         # 简单的健康检查
#         return {
#             "status": "healthy",
#             "service": "ppt_service",
#             "timestamp": datetime.now().isoformat(),
#             "version": "1.0.0"
#         }
#     except Exception as e:
#         logger.error(f"[PPT API] 健康检查失败: {str(e)}")
#         raise HTTPException(status_code=500, detail="PPT服务不可用")


# ==================== 通用错误处理 ====================
# 注意：异常处理器应该在主应用实例上注册，而不是在路由器上
