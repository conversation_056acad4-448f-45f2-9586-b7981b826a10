#!/usr/bin/env python3
"""
测试会话历史查询接口
使用指定的用户信息：ali_uid=1550203943326350, wy_id=test_user
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import Optional

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.domain.services.session_manager import SessionManager
    from src.infrastructure.memory.memory_sdk import MemorySDK
    from src.infrastructure.database.repositories.session_repository import session_db_service
    print("✓ 成功导入所需模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

async def test_memory_sdk_directly():
    """直接测试Memory SDK的get_session_messages方法"""
    print("\n=== 直接测试Memory SDK ===")
    
    try:
        memory_sdk = MemorySDK()
        print("✓ Memory SDK初始化成功")
        
        # 测试参数
        test_session_id = "test_session_1550203943326350"
        ali_uid = "1550203943326350"
        wy_id = "test_user"
        
        print(f"测试参数: session_id={test_session_id}, ali_uid={ali_uid}, wy_id={wy_id}")
        
        # 调用get_session_messages
        result = await memory_sdk.get_session_messages(
            session_id=test_session_id,
            page=1,
            page_size=20
        )
        
        if result:
            print(f"✓ 获取到会话消息:")
            print(f"  - 会话ID: {result.session_id}")
            print(f"  - 轮次数量: {result.total_rounds}")
            print(f"  - 分页信息: page={result.pagination.page}, page_size={result.pagination.page_size}, total={result.pagination.total}")
            
            for i, round_info in enumerate(result.rounds):
                print(f"  - Round {i+1}: round_id={round_info.round_id}, messages={len(round_info.messages)}")
                for j, msg in enumerate(round_info.messages[:2]):  # 只显示前2条消息
                    print(f"    - Message {j+1}: role={msg.role}, content={msg.content[:50]}...")
        else:
            print("✗ 未获取到会话消息")
            
    except Exception as e:
        print(f"✗ Memory SDK测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_session_manager():
    """测试SessionManager的get_session_history方法"""
    print("\n=== 测试SessionManager ===")
    
    try:
        # 创建SessionManager实例
        session_manager = SessionManager()
        print("✓ SessionManager初始化成功")
        
        # 测试参数
        test_session_id = "test_session_1550203943326350"
        ali_uid = "1550203943326350"
        wy_id = "test_user"
        
        print(f"测试参数: session_id={test_session_id}, ali_uid={ali_uid}, wy_id={wy_id}")
        
        # 首先检查数据库中是否有这个会话
        session_model = session_db_service.get_session_by_id(test_session_id)
        if session_model:
            print(f"✓ 数据库中找到会话: {session_model.session_id}")
            print(f"  - 标题: {session_model.title}")
            print(f"  - 状态: {session_model.status}")
            print(f"  - 创建时间: {session_model.gmt_create}")
        else:
            print(f"✗ 数据库中未找到会话: {test_session_id}")
            # 创建一个测试会话
            print("正在创建测试会话...")
            session_model = session_db_service.create_session(
                ali_uid=ali_uid,
                agent_id="test_agent",
                session_id=test_session_id,
                title="测试会话",
                metadata={"test": True}
            )
            print(f"✓ 创建测试会话成功: {session_model.session_id}")
        
        # 调用get_session_history
        session = session_manager.get_session_history(
            session_id=test_session_id,
            page=1,
            page_size=20
        )
        
        if session:
            print(f"✓ 获取到会话历史:")
            print(f"  - 会话ID: {session.session_id}")
            print(f"  - 用户ID: {session.ali_uid}")
            print(f"  - 标题: {session.title}")
            print(f"  - 状态: {session.status}")
            print(f"  - 轮次数量: {len(session.rounds)}")
            
            for i, round_obj in enumerate(session.rounds):
                print(f"  - Round {i+1}: round_id={round_obj.round_id}, messages={len(round_obj.messages)}")
                print(f"    - 用户提示: {round_obj.user_prompt}")
                print(f"    - 状态: {round_obj.status}")
                for j, msg in enumerate(round_obj.messages[:2]):  # 只显示前2条消息
                    print(f"    - Message {j+1}: role={msg.role}, content={msg.content[:50]}...")
        else:
            print("✗ 未获取到会话历史")
            
    except Exception as e:
        print(f"✗ SessionManager测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_sessions():
    """测试数据库中的会话数据"""
    print("\n=== 测试数据库会话数据 ===")
    
    try:
        ali_uid = "1550203943326350"
        
        # 查询该用户的所有会话
        sessions = session_db_service.list_sessions(
            limit=10,
            offset=0,
            ali_uid=ali_uid
        )
        
        print(f"✓ 用户 {ali_uid} 的会话数量: {len(sessions)}")
        
        for i, session in enumerate(sessions):
            print(f"  - 会话 {i+1}:")
            print(f"    - ID: {session.session_id}")
            print(f"    - 标题: {session.title}")
            print(f"    - Agent: {session.agent_id}")
            print(f"    - 状态: {session.status}")
            print(f"    - 创建时间: {session.gmt_create}")
            print(f"    - 修改时间: {session.gmt_modified}")
        
        # 获取总数
        total_count = session_db_service.count_sessions(ali_uid=ali_uid)
        print(f"✓ 用户 {ali_uid} 的会话总数: {total_count}")
        
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_api_simulation():
    """模拟API调用"""
    print("\n=== 模拟API调用 ===")
    
    try:
        # 模拟API参数
        session_id = "test_session_1550203943326350"
        page = 1
        page_size = 20
        before_round_id = None
        
        print(f"模拟API调用参数:")
        print(f"  - session_id: {session_id}")
        print(f"  - page: {page}")
        print(f"  - page_size: {page_size}")
        print(f"  - before_round_id: {before_round_id}")
        
        # 创建SessionManager并初始化
        session_manager = SessionManager()
        await session_manager.initialize()
        print("✓ SessionManager初始化完成")
        
        # 调用get_session_history方法
        session = session_manager.get_session_history(
            session_id=session_id,
            page=page,
            page_size=page_size,
            before_round_id=before_round_id
        )
        
        if session:
            print(f"✓ API调用成功，返回数据:")
            
            # 模拟API响应格式
            rounds_data = []
            for round_obj in session.rounds:
                messages_data = []
                for msg in round_obj.messages:
                    messages_data.append({
                        "roundId": msg.round_id,
                        "content": msg.content,
                        "role": msg.role.value if hasattr(msg.role, 'value') else str(msg.role),
                        "timestamp": msg.timestamp.isoformat() if msg.timestamp else datetime.now().isoformat(),
                        "sessionId": msg.session_id,
                        "name": msg.name,
                        "toolCallId": msg.tool_call_id,
                        "appId": msg.app_id,
                        "duration": msg.duration
                    })
                
                round_data = {
                    "roundId": round_obj.round_id,
                    "userPrompt": round_obj.user_prompt,
                    "status": round_obj.status.name.lower() if hasattr(round_obj.status, 'name') else str(round_obj.status),
                    "startTime": round_obj.start_time.isoformat() if round_obj.start_time else datetime.now().isoformat(),
                    "endTime": round_obj.end_time.isoformat() if round_obj.end_time else None,
                    "duration": str(round_obj.duration) if hasattr(round_obj, 'duration') and round_obj.duration else None,
                    "messages": messages_data
                }
                rounds_data.append(round_data)
            
            response_data = {
                "session_id": session.session_id,
                "ali_uid": session.ali_uid,
                "agent_id": session.agent_id,
                "title": session.title,
                "status": session.status.name.lower() if hasattr(session.status, 'name') else str(session.status),
                "gmt_create": session.gmt_create.isoformat() if session.gmt_create else datetime.now().isoformat(),
                "gmt_modified": session.gmt_modified.isoformat() if session.gmt_modified else datetime.now().isoformat(),
                "total_rounds": len(session.rounds),
                "rounds": rounds_data
            }
            
            print(f"  - 会话ID: {response_data['session_id']}")
            print(f"  - 用户ID: {response_data['ali_uid']}")
            print(f"  - 标题: {response_data['title']}")
            print(f"  - 状态: {response_data['status']}")
            print(f"  - 总轮数: {response_data['total_rounds']}")
            print(f"  - 返回轮数: {len(response_data['rounds'])}")
            
            # 保存响应到文件
            with open("../test_response.json", "w", encoding="utf-8") as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
            print("✓ 响应数据已保存到 test_response.json")
            
        else:
            print("✗ API调用失败，未返回数据")
            
    except Exception as e:
        print(f"✗ API模拟测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("开始测试会话历史查询接口")
    print(f"测试用户: ali_uid=1550203943326350, wy_id=test_user")
    print("=" * 60)
    
    # 1. 测试数据库会话数据
    test_database_sessions()
    
    # 2. 直接测试Memory SDK
    await test_memory_sdk_directly()
    
    # 3. 测试SessionManager
    test_session_manager()
    
    # 4. 模拟完整的API调用
    await test_api_simulation()
    
    print("\n" + "=" * 60)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
