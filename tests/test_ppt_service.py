#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPT Service 测试
"""

from src.domain.services.ppt_service import PPTService
from src.application.ppt_api_models import GetPPTAuthCodeResponse


def test_get_ppt_auth_code():
    """测试获取PPT认证码"""
    # 创建服务实例
    service = PPTService()

    # 调用API
    result = service.get_ppt_auth_code(ali_uid=123456)

    # 检查结果
    assert result is not None
    assert hasattr(result, 'code')
    assert hasattr(result, 'time_expire')
    assert hasattr(result, 'api_key')
    assert hasattr(result, 'channel')

    # 检查类型
    assert isinstance(result, GetPPTAuthCodeResponse)
    assert isinstance(result.code, str)
    assert isinstance(result.time_expire, str)
    assert isinstance(result.api_key, str)
    assert isinstance(result.channel, str)


def test_get_aippt_token():
    """测试获取AIPPT token"""
    service = PPTService()

    token = service._get_aippt_token()

    assert token is not None
    assert isinstance(token, str)
    assert len(token) > 0


if __name__ == "__main__":
    """
    用pytest运行：
    pytest tests/test_ppt_service.py -v
    """
    print("开始PPT服务测试...")
    print("=" * 50)

    try:
        test_get_ppt_auth_code()
        test_get_aippt_token()
        print("=" * 50)
        print("🎉 所有测试通过！")

    except Exception as e:
        print("=" * 50)
        print(f"❌ 测试失败: {e}")
        raise
