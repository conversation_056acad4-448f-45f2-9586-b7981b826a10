# 用基础镜像地址替换下方镜像地址
FROM reg.docker.alibaba-inc.com/amwp/kubeone-base:1.8_215e9f90_2025-03-19

# 指定pypi镜像信息
ENV PYPI_MIRROR http://artlab.alibaba-inc.com/1/pypi/simple
ENV PYPI_MIRROR_HOST artlab.alibaba-inc.com

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ENV APP_NAME wuying-alpha-service
ENV APP_HOME /home/<USER>/$APP_NAME
ENV ADMIN_HOME /home/<USER>

# 构建时要做的事，一般是执行shell命令，例如用来安装必要软件，创建文件（夹），修改文件
RUN sudo yum update -y -v
RUN sudo yum clean all
RUN sudo yum makecache
RUN sudo yum install libffi-devel openssl-devel openssl-1.1.1 -y -b current
RUN sudo yum install -y zeromq-devel gcc-c++ make libatomic python3.12-devel

# 安装python
RUN sudo yum install -y python-3.12.5 -b current
RUN pip3 install --upgrade pip -i ${PYPI_MIRROR} --trusted-host ${PYPI_MIRROR_HOST}

RUN sudo yum install tengine-proxy-2.4.5  -b current -y
RUN sudo yum install taobao-cronolog-1.6.2 -b current -y

RUN sudo chown -R admin:admin $ADMIN_HOME
RUN sudo chmod -R 755 $ADMIN_HOME

RUN mkdir -p $APP_HOME/base && \
    mkdir -p $APP_HOME/custom && \
    mkdir -p $APP_HOME/bin && \
    mkdir -p $APP_HOME/conf && \
    mkdir -p $APP_HOME/target && \
    mkdir -p $ADMIN_HOME/logs && \
    mkdir -p $ADMIN_HOME/cai/htdocs && \
    echo "$APP_HOME/base/bin/appctl.sh start >> $APP_HOME/logs/deploy.log" >> $ADMIN_HOME/start.sh && \
    echo "$APP_HOME/base/bin/appctl.sh status" >> $ADMIN_HOME/health.sh && \
    echo "$APP_HOME/base/bin/appctl.sh stop" >> $ADMIN_HOME/stop.sh

COPY $APP_NAME.tgz $APP_HOME/target/
COPY environment/common/base/cai $ADMIN_HOME/cai/
COPY environment/common/base/app $APP_HOME/base/
COPY environment/common/custom/app $APP_HOME/custom/

# 设置文件操作权限
RUN sudo chmod -R a+x $APP_HOME/base/bin/ && \
    sudo chmod -R a+x $APP_HOME/custom/bin/ && \
    sudo chmod -R a+x $ADMIN_HOME/cai/bin/ && \
    sudo chmod +x $ADMIN_HOME/*.sh && \
     ln -s $APP_HOME/base/bin/appctl.sh $APP_HOME/bin/appctl.sh && \
     ln -s $APP_HOME/base/bin/start.sh $APP_HOME/bin/start.sh && \
     ln -s $APP_HOME/base/bin/stop.sh $APP_HOME/bin/stop.sh && \
     ln -s $APP_HOME/base/bin/health.sh $APP_HOME/bin/health.sh && \
     tar -xzf $APP_HOME/target/$APP_NAME.tgz -C $ADMIN_HOME $APP_NAME/conf && \
     tar -xzf $APP_HOME/target/$APP_NAME.tgz $APP_NAME/antx.properties -O > $APP_HOME/target/antx.properties && \
     tar -xzf $APP_HOME/target/$APP_NAME.tgz -C $APP_HOME --strip-components=1

# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME $ADMIN_HOME/logs \
       $ADMIN_HOME/cai/logs \
       $APP_HOME/logs

RUN pip3 install -r $APP_HOME/conf/requirements.txt -i ${PYPI_MIRROR} --trusted-host ${PYPI_MIRROR_HOST} --only-binary :all:
# 先安装alibabacloud-endpoint-util避免版本冲突
RUN pip3 install -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com "alibabacloud-endpoint-util>=0.0.3,<1.0.0"

# 安装keycenter, waiy-memory和阿里云SDK相关包
# RUN pip3 install -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com keycenter-office==2.3.9 waiy-memory==0.1.25 aliyun-akless-credential-provider-python-sdk==1.10.1 alibabacloud-credentials alibabacloud-tea-openapi==0.4.0rc4 alibabacloud-tea-util alibabacloud-ons20190214==2.0.1


RUN pip3 install oss2 -i https://pypi.tuna.tsinghua.edu.cn/simple

RUN ls -l $APP_HOME

RUN pip3 install -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com  numpy --only-binary :all:

RUN pip3 install -e $APP_HOME -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 设置 PATH 环境变量
ENV PATH="/usr/local/python/python3.12/bin:${PATH}"

# 安装popclients
RUN cd $APP_HOME/src/popclients/wuying-pc-inside-20221123 && pip3 install . -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
RUN cd $APP_HOME/src/popclients/wuyingaiinner-20250718 && pip3 install . -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
RUN cd $APP_HOME/src/popclients/appstream-center-inner-20211212 && pip3 install . -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 启动容器时进入的工作目录
WORKDIR $APP_HOME/
